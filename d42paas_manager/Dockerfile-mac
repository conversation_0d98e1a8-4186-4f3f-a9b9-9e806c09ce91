# 构建阶段
FROM maven:3.8-openjdk-17 AS builder

# 设置工作目录
WORKDIR /build

# 首先只复制 pom 文件
COPY ../pom.xml .
COPY ../d42paas_common/pom.xml d42paas_common/
COPY ../d42paas_manager/pom.xml d42paas_manager/
COPY ../d42paas_demo/pom.xml d42paas_demo/
COPY ../d42paas_admin/pom.xml d42paas_admin/

# 下载依赖（这步会被缓存）
RUN mvn dependency:go-offline

# 然后再复制源代码
COPY .. .

# 构建项目
RUN mvn clean package -Dmaven.test.skip=true -Dspring.redis.host=host.docker.internal

# 运行阶段
FROM amazoncorretto:17-alpine-jdk

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 安装必要的包
RUN apk add --no-cache tini curl openssh && addgroup --gid 2000 runner && adduser --disabled-password -u 2000 -G runner runner

# 复制arthas
COPY --from=hengyunabc/arthas:latest /opt/arthas /opt/arthas

# 复制OpenTelemetry agent
ADD https://github.com/open-telemetry/opentelemetry-java-instrumentation/releases/latest/download/opentelemetry-javaagent.jar /app/opentelemetry-javaagent.jar
RUN chmod +r /app/opentelemetry-javaagent.jar

# 从构建阶段复制jar文件
COPY --from=builder /build/d42paas_manager/target/manager.jar /app/d42paas-manager.jar

WORKDIR /app
RUN chown -R runner:runner /app

ENTRYPOINT ["/sbin/tini", "--"]

# 设置启动用户为runner
USER runner

# 设置Redis连接地址
# ENV SPRING_REDIS_HOST=host.docker.internal

# 生产环境启动命令
CMD ["java", "-jar", "-Duser.timezone=Asia/Shanghai", "d42paas-manager.jar"]