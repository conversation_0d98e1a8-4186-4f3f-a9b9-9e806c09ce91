#!/bin/bash

# 简化版热重载脚本 - 基于轮询的文件监控
# 适用于不支持inotify的环境

set -e

APP_NAME="d42paas-manager"
JAR_FILE="/app/d42paas_manager/target/manager.jar"
PID_FILE="/tmp/${APP_NAME}.pid"
LOG_FILE="/tmp/${APP_NAME}.log"
BUILD_LOG="/tmp/build.log"
CHECKSUM_FILE="/tmp/source_checksum.txt"

# 监控目录
WATCH_DIRS="/app/d42paas_manager/src /app/d42paas_common/src"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

log_build() {
    echo -e "${BLUE}[BUILD]${NC} $(date '+%Y-%m-%d %H:%M:%S') $1"
}

# 计算源码文件的校验和
calculate_checksum() {
    find $WATCH_DIRS -name "*.java" -o -name "*.xml" -o -name "*.yml" -o -name "*.yaml" -o -name "*.properties" 2>/dev/null | \
    xargs ls -la 2>/dev/null | \
    md5sum | \
    cut -d' ' -f1
}

# 停止应用
stop_app() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            log_info "停止应用 (PID: $pid)"
            kill "$pid"
            # 等待进程结束
            local count=0
            while kill -0 "$pid" 2>/dev/null && [ $count -lt 30 ]; do
                sleep 1
                count=$((count + 1))
            done
            if kill -0 "$pid" 2>/dev/null; then
                log_warn "强制停止应用"
                kill -9 "$pid"
            fi
        fi
        rm -f "$PID_FILE"
    fi
}

# 编译项目
build_project() {
    log_build "开始编译项目..."
    cd /app
    
    # 清理并编译
    if mvn clean package -Dmaven.test.skip=true  > "$BUILD_LOG" 2>&1; then
        log_build "编译成功"
        return 0
    else
        log_error "编译失败，查看日志: $BUILD_LOG"
        tail -20 "$BUILD_LOG"
        return 1
    fi
}

# 启动应用
start_app() {
    if [ ! -f "$JAR_FILE" ]; then
        log_error "JAR文件不存在: $JAR_FILE"
        return 1
    fi
    
    log_info "启动应用..."
    cd /app
    
    # 启动Java应用
    nohup java -jar \
        -Duser.timezone=Asia/Shanghai \
        -Dspring.profiles.active=local \
        "$JAR_FILE" > "$LOG_FILE" 2>&1 &
    
    local pid=$!
    echo "$pid" > "$PID_FILE"
    
    # 等待应用启动
    log_info "等待应用启动 (PID: $pid)..."
    sleep 5
    
    if kill -0 "$pid" 2>/dev/null; then
        log_info "应用启动成功"
        return 0
    else
        log_error "应用启动失败"
        tail -20 "$LOG_FILE"
        return 1
    fi
}

# 重启应用
restart_app() {
    log_info "重启应用..."
    stop_app
    if build_project; then
        start_app
        # 更新校验和
        calculate_checksum > "$CHECKSUM_FILE"
    else
        log_error "编译失败，应用未启动"
    fi
}

# 监控文件变化（轮询方式）
watch_files_polling() {
    log_info "开始监控文件变化（轮询模式，每5秒检查一次）..."
    log_info "监控目录: $WATCH_DIRS"
    
    # 初始化校验和
    calculate_checksum > "$CHECKSUM_FILE"
    
    while true; do
        sleep 5
        
        local current_checksum=$(calculate_checksum)
        local last_checksum=""
        
        if [ -f "$CHECKSUM_FILE" ]; then
            last_checksum=$(cat "$CHECKSUM_FILE")
        fi
        
        if [ "$current_checksum" != "$last_checksum" ]; then
            log_info "检测到文件变化"
            restart_app
        fi
    done
}

# 清理函数
cleanup() {
    log_info "收到退出信号，清理资源..."
    stop_app
    exit 0
}

# 设置信号处理
trap cleanup SIGTERM SIGINT

# 主函数
main() {
    log_info "=== 开发环境热重载启动（简化版） ==="
    log_info "应用名称: $APP_NAME"
    log_info "JAR文件: $JAR_FILE"
    log_info "日志文件: $LOG_FILE"
    
    # 初始编译和启动
    if build_project && start_app; then
        log_info "初始启动完成"
        
        # 显示应用日志（后台）
        tail -f "$LOG_FILE" &
        
        # 开始监控文件变化
        watch_files_polling
    else
        log_error "初始启动失败"
        exit 1
    fi
}

# 启动主函数
main "$@"
