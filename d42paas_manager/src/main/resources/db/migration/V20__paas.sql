-- 创建 meta_environment 表
CREATE TABLE `meta_environment` (
  `id` bigint(20) NOT NULL,
  `created_by` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `last_modified_by` varchar(255) DEFAULT NULL,
  `last_modified_date` datetime DEFAULT NULL,
  `version` bigint(20) NOT NULL,
  `deleted` bit(1) DEFAULT b'0',
  `name` varchar(100) NOT NULL,
  `environment_version` varchar(50) NOT NULL,
  `s3_path` varchar(500) NOT NULL,
  `local_cache_path` varchar(500) DEFAULT NULL,
  `size_bytes` bigint(20) DEFAULT NULL,
  `file_count` int(11) DEFAULT NULL,
  `status` varchar(50) NOT NULL,
  `description` varchar(1000) DEFAULT NULL,
  `is_default` bit(1) DEFAULT b'0',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `last_synced_at` datetime DEFAULT NULL,
  PRIMAR<PERSON> (`id`),
  UNIQUE KEY `UK_meta_env_name_version` (`name`, `environment_version`),
  INDEX `idx_meta_env_status` (`status`),
  INDEX `idx_meta_env_is_default` (`is_default`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
