package com.dao42.paas.service.docker;

import com.dao42.paas.exception.resource.NoDockerServerException;
import com.dao42.paas.exception.resource.ResourceNotEnoughException;
import com.dao42.paas.framework.jpa.model.AbstractAuditModel;
import com.dao42.paas.model.docker.DockerContainer;
import com.dao42.paas.model.middleware.MiddlewareInstance;
import com.dao42.paas.service.impl.MemoryDockerServerSelector;

import java.util.ArrayList;
import java.util.List;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * 容器资源管理Service
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DockerResourceService {

    private final MemoryDockerServerSelector dockerServerSelector;

    public DockerResourceService(@Qualifier("memoryDockerServerSelector") MemoryDockerServerSelector dockerServerSelector) {
        this.dockerServerSelector = dockerServerSelector;
    }

    /**
     * 请求内存和cpu资源（包括主容器与中间件）
     *
     * @param docker 主容器
     */
    public void chooseAllServer(DockerContainer docker) throws NoDockerServerException {
        try {
            for (MiddlewareInstance middleware : docker.getMiddlewares()) {
                dockerServerSelector.select(middleware);
            }
            dockerServerSelector.select(docker);
        } catch (NoDockerServerException e) {
            // 有容器无法获取宿主机，回收其他已获取宿主机的容器
            docker.getMiddlewares().forEach(dockerServerSelector::recycle);
            dockerServerSelector.recycle(docker);
            log.debug("DockerScheduling-Active, chooseAllServer; docker_id:{}; exception: {}",
                docker.getId(), e);
            throw e;
        }
    }

    /**
     * 请求内存和cpu资源（中间件）
     *
     * @param middlewareInstance 主容器
     */
    public void chooseServerForMiddleware(MiddlewareInstance middlewareInstance) throws NoDockerServerException {
        try {
            dockerServerSelector.select(middlewareInstance);
        } catch (NoDockerServerException e) {
            // 有容器无法获取宿主机，回收其他已获取宿主机的容器
            dockerServerSelector.recycle(middlewareInstance);
            throw e;
        }
    }

    /**
     * 验证宿主机是否可用
     *
     * @param docker 主容器
     * @return 重新绑定了宿主机的对象（主容器或宿主机）
     * @throws NoDockerServerException 没有可用的宿主机
     */
    public List<AbstractAuditModel> checkAllServer(DockerContainer docker) throws NoDockerServerException {
        // 原宿主机申请资源成功的
        List<AbstractAuditModel> successList = new ArrayList<>();
        // 重新绑定宿主机的
        List<AbstractAuditModel> rebindList = new ArrayList<>();
        try {
            // 1.判断中间件的原宿主机是否可用
            for (MiddlewareInstance middleware : docker.getMiddlewares()) {
                try {
                    dockerServerSelector.requestOriginalServer(middleware);
                    // 原宿主机资源申请成功，计入成功集合
                    successList.add(middleware);
                } catch (ResourceNotEnoughException e) {
                    // 原宿主机资源不足，重新选择宿主机
                    middleware.setServer(null);
                    dockerServerSelector.select(middleware);
                    // 新宿主机资源申请成功，计入成功集合
                    successList.add(middleware);
                    rebindList.add(middleware);
                }
            }
            // 2.判断主容器的原宿主机是否可用
            try {
                dockerServerSelector.requestOriginalServer(docker);
            } catch (ResourceNotEnoughException e) {
                // 原宿主机资源不足，重新选择宿主机
                docker.setDockerServer(null);
                dockerServerSelector.select(docker);
                successList.add(docker);
                rebindList.add(docker);
            }
        } catch (NoDockerServerException e) {
            // 3.有容器最终没有可绑定宿主机，归还其他容器的资源
            docker.getMiddlewares().stream().filter(successList::contains).forEach(dockerServerSelector::recycle);
            throw e;
        }
        return rebindList;
    }

    /**
     * 回收所有资源（包括主容器和中间件）
     *
     * @param docker 主容器
     */
    public void recycleAll(DockerContainer docker) {
        docker.getMiddlewares().forEach(this::recycleSingle);
        this.recycleSingle(docker);
    }

    /**
     * 回收资源
     *
     * @param instance 中间件
     */
    public void recycleSingle(MiddlewareInstance instance) {
        dockerServerSelector.recycle(instance);
    }

    /**
     * 回收资源
     *
     * @param docker 主容器
     */
    public void recycleSingle(DockerContainer docker) {
        dockerServerSelector.recycle(docker);
    }
}
