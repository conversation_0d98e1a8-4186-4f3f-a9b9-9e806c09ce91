# CodeZone 镜像化性能优化技术方案

## 1. 方案概述

### 1.1 问题分析

当前 Clacky AI PaaS 系统在高并发场景下存在以下性能瓶颈：

- **NFS 存储瓶颈**：500 并发时出现 40%+ 容器激活超时
- **启动时间过长**：容器启动时间 50s-120s，无法满足秒起需求
- **中心化存储压力**：所有容器依赖单一 NFS 存储，成为性能瓶颈
- **扩展性限制**：无法支持 10000 级并发的目标

### 1.2 设计目标

- **性能提升**：从 500 并发提升到 10000 级并发，实现秒起
- **存储分流**：通过镜像化减少 NFS 存储压力
- **用户无感**：自动化镜像管理，用户体验不变
- **成本优化**：智能镜像生命周期管理，控制存储成本
- **高可用性**：分布式镜像存储，提高系统可靠性

### 1.3 核心策略

1. **Thread 级镜像化**：Root Thread 和 Issue Thread 分别镜像化
2. **@meta 环境分离**：通过 Overlay 文件系统注入公共环境
3. **智能调度优化**：基于镜像缓存的亲和性调度
4. **自动化生命周期**：容器停止时自动创建和推送镜像

## 2. 技术架构设计

### 2.1 整体架构演进

```
原架构 (NFS 中心化):
┌─────────────────────────────────────────────────────────────────┐
│                    NFS Server with BTRFS                       │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                @meta (30G 模板)                             │ │
│  └─────────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │            CodeZone Instances (BTRFS Fork)                 │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                │ 性能瓶颈
                                ▼
新架构 (分布式镜像):
┌─────────────────────────────────────────────────────────────────┐
│                    容器镜像注册中心 (ECR/Harbor)                  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   Base Images   │  │ Root Thread     │  │ Issue Thread    │  │
│  │                 │  │   Images        │  │   Images        │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
                                │ 分布式拉取
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Docker Server 集群                          │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │  Docker Host 1  │  │  Docker Host 2  │  │  Docker Host N  │  │
│  │  ┌───────────┐  │  │  ┌───────────┐  │  │  ┌───────────┐  │  │
│  │  │Local Cache│  │  │  │Local Cache│  │  │  │Local Cache│  │  │
│  │  │+ @meta SSD│  │  │  │+ @meta SSD│  │  │  │+ @meta SSD│  │  │
│  │  └───────────┘  │  │  └───────────┘  │  │  └───────────┘  │  │
│  │  ┌───────────┐  │  │  ┌───────────┐  │  │  ┌───────────┐  │  │
│  │  │Container 1│  │  │  │Container 3│  │  │  │Container N│  │  │
│  │  │(Overlay)  │  │  │  │(Overlay)  │  │  │  │(Overlay)  │  │  │
│  │  └───────────┘  │  │  └───────────┘  │  │  └───────────┘  │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

### 2.2 镜像分层策略

#### 2.2.1 三层镜像架构

```
Layer 1: Base Image 
├── Ubuntu 20.04 基础系统
├── 预装开发工具 (git, curl, wget, etc.)
├── 多语言运行时 (Node.js, Python, Go, Java, etc.)
├── APT 包缓存 (2.7G)
├── 用户主目录模板 (4.0G)
└── 系统配置和工具

Layer 2: Root Thread Image (项目级)
├── FROM base-meta:latest
├── 项目特定依赖 (package.json, requirements.txt)
├── 项目配置文件
├── 预编译资源
└── 项目级环境变量

Layer 3: Issue Thread Image (分支级)
├── FROM root-thread:project-id-version
├── 分支特定代码
├── Issue 特定配置
├── 开发依赖
└── 调试工具
```

## 3. 核心组件设计

### 3.1 镜像生命周期管理器

```java
@Service
public class ThreadImageLifecycleManager {

    @Autowired
    private DockerImageService dockerImageService;

    @Autowired
    private ImageRegistryService imageRegistryService;

    @Autowired
    private SystemProperties systemProperties;

    /**
     * 容器停止时自动创建镜像 (用户无感)
     */
    @EventListener
    @Async
    public void onContainerStopped(ContainerStoppedEvent event) {
        DockerContainer container = event.getContainer();

        // 只对 STOP_SUCCESS 状态的容器创建镜像
        if (container.getStatus() == DockerContainerStatus.STOP_SUCCESS) {
            createThreadImage(container);
        }
    }

    private void createThreadImage(DockerContainer container) {
        try {
            // 1. 生成镜像标签
            String imageTag = generateImageTag(container);

            // 2. 执行 docker commit
            String imageId = dockerImageService.commit(
                container.getContainerId(),
                imageTag,
                CommitOptions.builder()
                    .author("Clacky PaaS System")
                    .message("Auto-generated thread image")
                    .pause(true) // 暂停容器以确保一致性
                    .build()
            );

            // 3. 推送到镜像仓库
            pushImageAsync(imageTag, container);

            // 4. 更新数据库记录
            saveImageRecord(container, imageTag, imageId);

            // 5. 清理本地镜像 (可选)
            if (systemProperties.getImage().isAutoCleanup()) {
                dockerImageService.removeLocal(imageId);
            }

            log.info("Thread 镜像创建完成: container={}, image={}",
                    container.getId(), imageTag);

        } catch (Exception e) {
            log.error("Thread 镜像创建失败: container={}", container.getId(), e);
        }
    }

    private String generateImageTag(DockerContainer container) {
        String registry = systemProperties.getImage().getRegistry();
        String namespace = systemProperties.getImage().getNamespace();

        if (container.getType() == ContainerType.ROOT_THREAD) {
            return String.format("%s/%s/root-thread:%s-%s",
                registry, namespace,
                container.getProjectId(),
                System.currentTimeMillis());
        } else {
            return String.format("%s/%s/issue-thread:%s-%s-%s",
                registry, namespace,
                container.getProjectId(),
                container.getIssueId(),
                System.currentTimeMillis());
        }
    }

    @Async
    private void pushImageAsync(String imageTag, DockerContainer container) {
        try {
            imageRegistryService.push(imageTag);
            log.info("镜像推送完成: {}", imageTag);
        } catch (Exception e) {
            log.error("镜像推送失败: {}", imageTag, e);
        }
    }
}
```

### 3.2 智能调度器 (基于镜像缓存亲和性)

```java
@Service
public class ImageAffinityScheduler extends MemoryDockerServerSelector {

    @Autowired
    private ImageCacheService imageCacheService;

    @Autowired
    private ThreadImageRepository threadImageRepository;

    @Override
    public DockerServer selectServer(DockerContainer container, ResourcesLimit resources) {
        // 1. 获取候选服务器列表 (满足资源需求)
        List<DockerServer> candidates = getCandidateServers(resources);

        // 2. 检查是否有可用的 Thread 镜像
        Optional<String> threadImage = findAvailableThreadImage(container);

        if (threadImage.isPresent()) {
            // 3. 优先选择已缓存该镜像的服务器
            Optional<DockerServer> cachedServer = findServerWithImage(candidates, threadImage.get());
            if (cachedServer.isPresent()) {
                log.info("选择缓存命中服务器: server={}, image={}",
                        cachedServer.get().getId(), threadImage.get());
                return cachedServer.get();
            }
        }

        // 4. 降级到内存负载调度
        DockerServer selected = super.selectServer(container, resources);

        // 5. 异步预拉取镜像
        if (threadImage.isPresent()) {
            preloadImageAsync(selected.getId(), threadImage.get());
        }

        return selected;
    }

    private Optional<String> findAvailableThreadImage(DockerContainer container) {
        if (container.getType() == ContainerType.ISSUE_THREAD) {
            // 查找 Issue Thread 镜像
            return threadImageRepository.findLatestIssueThreadImage(
                container.getProjectId(), container.getIssueId());
        } else if (container.getType() == ContainerType.ROOT_THREAD) {
            // 查找 Root Thread 镜像
            return threadImageRepository.findLatestRootThreadImage(
                container.getProjectId());
        }
        return Optional.empty();
    }

    private Optional<DockerServer> findServerWithImage(List<DockerServer> candidates, String imageTag) {
        return candidates.stream()
            .filter(server -> imageCacheService.hasImage(server.getId(), imageTag))
            .max(Comparator.comparing(server ->
                imageCacheService.getImageScore(server.getId(), imageTag)));
    }

    @Async
    private void preloadImageAsync(String serverId, String imageTag) {
        try {
            imageCacheService.preloadImage(serverId, imageTag);
            log.info("镜像预拉取完成: server={}, image={}", serverId, imageTag);
        } catch (Exception e) {
            log.error("镜像预拉取失败: server={}, image={}", serverId, imageTag, e);
        }
    }
}
```

### 3.3 @meta 环境注入器 (Overlay 文件系统)

```java
@Service
public class MetaEnvironmentInjector {

    @Autowired
    private S3Service s3Service;

    @Autowired
    private LocalCacheService localCacheService;

    @Autowired
    private SystemProperties systemProperties;

    /**
     * 注入 @meta 环境到容器 (只读层)
     */
    public List<Bind> injectMetaEnvironment(DockerContainer container) {
        List<Bind> binds = new ArrayList<>();

        // 1. 确保本地有最新的 @meta 环境
        String localMetaPath = ensureLocalMetaEnvironment(container.getDockerServerId());

        // 2. 创建 Overlay 文件系统
        OverlayConfig overlayConfig = createOverlayFileSystem(container, localMetaPath);

        // 3. 挂载 Overlay 文件系统
        binds.add(Bind.parse(overlayConfig.getMergedDir() + ":/home/<USER>"));

        // 4. 挂载只读的 @meta 环境 (备用访问)
        binds.add(Bind.parse(localMetaPath + ":/meta:ro"));

        return binds;
    }

    /**
     * 确保本地有最新的 @meta 环境
     */
    private String ensureLocalMetaEnvironment(String dockerServerId) {
        String localPath = "/cache/meta-" + dockerServerId;
        String s3Key = "meta-environment/latest.tar.gz";

        // 检查本地缓存是否最新 (30天更新周期)
        if (!localCacheService.isLatest(localPath, s3Key, Duration.ofDays(30))) {
            synchronized (this) {
                // 双重检查锁定
                if (!localCacheService.isLatest(localPath, s3Key, Duration.ofDays(30))) {
                    log.info("更新 @meta 环境: server={}", dockerServerId);

                    // 从 S3 下载最新 @meta 环境
                    s3Service.downloadAndExtract(s3Key, localPath);
                    localCacheService.updateTimestamp(localPath);

                    // 设置正确的权限
                    setMetaPermissions(localPath);
                }
            }
        }

        return localPath;
    }

    /**
     * 创建 Overlay 文件系统
     */
    private OverlayConfig createOverlayFileSystem(DockerContainer container, String metaPath) {
        String containerId = container.getContainerId();
        String overlayBaseDir = "/var/lib/docker/overlay2/" + containerId;

        OverlayConfig config = OverlayConfig.builder()
            .lowerDir(metaPath + "/dependency/home")  // @meta 只读层
            .upperDir(overlayBaseDir + "/upper")      // 用户可写层
            .workDir(overlayBaseDir + "/work")        // overlay 工作目录
            .mergedDir(overlayBaseDir + "/merged")    // 合并视图
            .build();

        // 创建必要的目录
        createDirectories(config.getUpperDir(), config.getWorkDir(), config.getMergedDir());

        // 挂载 Overlay 文件系统
        mountOverlayFileSystem(config);

        return config;
    }

    private void mountOverlayFileSystem(OverlayConfig config) {
        String mountCmd = String.format(
            "mount -t overlay overlay -o lowerdir=%s,upperdir=%s,workdir=%s %s",
            config.getLowerDir(),
            config.getUpperDir(),
            config.getWorkDir(),
            config.getMergedDir()
        );

        try {
            ProcessBuilder pb = new ProcessBuilder("bash", "-c", mountCmd);
            Process process = pb.start();
            int exitCode = process.waitFor();

            if (exitCode != 0) {
                throw new RuntimeException("Overlay 文件系统挂载失败: " + exitCode);
            }

            log.debug("Overlay 文件系统挂载成功: {}", config.getMergedDir());

        } catch (Exception e) {
            log.error("Overlay 文件系统挂载失败", e);
            throw new RuntimeException("Overlay 挂载失败", e);
        }
    }
}
```

### 3.4 热点镜像缓存管理器

```java
@Service
public class HotImageCacheManager {

    @Autowired
    private ImageUsageAnalyzer usageAnalyzer;

    @Autowired
    private DockerImageService dockerImageService;

    /**
     * 热点镜像预拉取 (提升命中率)
     */
    @Scheduled(fixedRate = 300000) // 5分钟执行一次
    public void preloadHotImages() {
        // 1. 分析镜像使用热度
        List<ImageHotness> hotImages = usageAnalyzer.analyzeImageHotness();

        // 2. 获取所有活跃的 Docker Server
        List<DockerServer> servers = dockerServerService.getAllActiveServers();

        // 3. 为每个热点镜像在所有服务器上预拉取
        for (ImageHotness hotness : hotImages) {
            if (hotness.getScore() > HOT_THRESHOLD) {
                preloadImageToServers(hotness.getImageTag(), servers);
            }
        }
    }

    /**
     * 智能缓存清理 (节省存储费用)
     */
    @Scheduled(cron = "0 2 * * * ?") // 每天凌晨2点
    public void cleanupUnusedImages() {
        List<DockerServer> servers = dockerServerService.getAllActiveServers();

        for (DockerServer server : servers) {
            cleanupServerImages(server);
        }
    }

    private void cleanupServerImages(DockerServer server) {
        try {
            // 获取服务器上的镜像列表
            List<ImageInfo> localImages = dockerImageService.listImages(server.getId());

            for (ImageInfo image : localImages) {
                // 检查镜像使用情况
                ImageUsageInfo usage = usageAnalyzer.getImageUsage(image.getTag());

                boolean shouldDelete = false;

                // 删除条件1: 超过30天未使用
                if (usage.getLastUsed().isBefore(LocalDateTime.now().minusDays(30))) {
                    shouldDelete = true;
                }

                // 删除条件2: 用户级别限制 (差分存储服务)
                if (isUserLevelLimited(image.getTag()) &&
                    exceedsUserImageLimit(image.getTag())) {
                    shouldDelete = true;
                }

                // 删除条件3: 功能开关控制
                if (systemProperties.getImage().isAutoCleanupEnabled() && shouldDelete) {
                    dockerImageService.removeImage(server.getId(), image.getTag());
                    log.info("清理未使用镜像: server={}, image={}",
                            server.getId(), image.getTag());
                }
            }

        } catch (Exception e) {
            log.error("清理服务器镜像失败: server={}", server.getId(), e);
        }
    }

    private boolean exceedsUserImageLimit(String imageTag) {
        // 解析镜像标签获取用户信息
        ImageTagInfo tagInfo = parseImageTag(imageTag);

        // 获取用户级别配置
        UserLevel userLevel = userService.getUserLevel(tagInfo.getUserId());

        // 检查是否超过用户镜像存储限制
        int currentImageCount = threadImageRepository.countByUserId(tagInfo.getUserId());
        return currentImageCount > userLevel.getMaxImageCount();
    }

    @Async
    private void preloadImageToServers(String imageTag, List<DockerServer> servers) {
        for (DockerServer server : servers) {
            try {
                if (!imageCacheService.hasImage(server.getId(), imageTag)) {
                    dockerImageService.pullImage(server.getId(), imageTag);
                    log.info("热点镜像预拉取完成: server={}, image={}",
                            server.getId(), imageTag);
                }
            } catch (Exception e) {
                log.warn("热点镜像预拉取失败: server={}, image={}",
                        server.getId(), imageTag, e);
            }
        }
    }
}
```

## 4. 性能优化策略

### 4.1 容器秒起优化

```java
@Service
public class FastContainerStartup {

    /**
     * 容器快速启动流程
     */
    public DockerContainer fastStartContainer(ContainerCreateRequest request) {
        // 1. 并行执行准备工作
        CompletableFuture<DockerServer> serverFuture = CompletableFuture.supplyAsync(() -> {
            return imageAffinityScheduler.selectServer(request);
        });

        CompletableFuture<String> imageFuture = CompletableFuture.supplyAsync(() -> {
            return determineOptimalImage(request);
        });

        CompletableFuture<List<Bind>> bindsFuture = CompletableFuture.supplyAsync(() -> {
            return metaEnvironmentInjector.injectMetaEnvironment(request);
        });

        CompletableFuture<NetworkConfig> networkFuture = CompletableFuture.supplyAsync(() -> {
            return networkService.allocateNetwork(request);
        });

        // 2. 等待所有准备工作完成
        CompletableFuture.allOf(serverFuture, imageFuture, bindsFuture, networkFuture).join();

        DockerServer server = serverFuture.get();
        String imageTag = imageFuture.get();
        List<Bind> binds = bindsFuture.get();
        NetworkConfig network = networkFuture.get();

        // 3. 确保镜像可用 (如果本地没有则快速拉取)
        ensureImageAvailable(server.getId(), imageTag);

        // 4. 创建并启动容器
        return createAndStartContainer(server, imageTag, binds, network, request);
    }

    /**
     * 确定最优镜像策略
     */
    private String determineOptimalImage(ContainerCreateRequest request) {
        // 1. 检查是否有可用的 Thread 镜像
        Optional<String> threadImage = findLatestThreadImage(request);
        if (threadImage.isPresent()) {
            return threadImage.get();
        }

        // 2. 检查是否有 Root Thread 镜像
        if (request.getType() == ContainerType.ISSUE_THREAD) {
            Optional<String> rootImage = findRootThreadImage(request.getProjectId());
            if (rootImage.isPresent()) {
                return rootImage.get();
            }
        }

        // 3. 降级到基础镜像
        return getBaseImage(request.getEnvironmentVersion());
    }

    /**
     * 快速镜像拉取 (如果需要)
     */
    private void ensureImageAvailable(String serverId, String imageTag) {
        if (!imageCacheService.hasImage(serverId, imageTag)) {
            // 并行拉取镜像
            CompletableFuture<Void> pullFuture = CompletableFuture.runAsync(() -> {
                dockerImageService.pullImage(serverId, imageTag);
            });

            // 设置超时时间 (最多等待30秒)
            try {
                pullFuture.get(30, TimeUnit.SECONDS);
            } catch (TimeoutException e) {
                log.warn("镜像拉取超时，使用基础镜像: server={}, image={}",
                        serverId, imageTag);
                // 降级到基础镜像
                throw new ImagePullTimeoutException("镜像拉取超时", e);
            }
        }
    }
}
```

### 4.2 镜像存储管理策略

```java
@Service
public class ImageStorageManager {

    /**
     * 不活跃 Thread 删除策略
     */
    @Scheduled(cron = "0 3 * * * ?") // 每天凌晨3点
    public void cleanupInactiveThreads() {
        if (!systemProperties.getImage().isInactiveCleanupEnabled()) {
            return;
        }

        int inactiveDays = systemProperties.getImage().getInactiveCleanupDays(); // 默认30天
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(inactiveDays);

        // 查找不活跃的 Thread 镜像
        List<ThreadImage> inactiveImages = threadImageRepository
            .findInactiveImages(cutoffTime);

        for (ThreadImage image : inactiveImages) {
            try {
                // 删除镜像仓库中的镜像
                imageRegistryService.deleteImage(image.getImageTag());

                // 删除所有 Docker Server 上的本地缓存
                deleteImageFromAllServers(image.getImageTag());

                // 删除数据库记录
                threadImageRepository.delete(image);

                log.info("删除不活跃 Thread 镜像: {}", image.getImageTag());

            } catch (Exception e) {
                log.error("删除不活跃镜像失败: {}", image.getImageTag(), e);
            }
        }
    }

    /**
     * 用户级别差分存储服务
     */
    public void enforceUserStorageQuota(Long userId) {
        UserLevel userLevel = userService.getUserLevel(userId);
        int maxImages = userLevel.getMaxThreadImages();

        // 获取用户的所有 Thread 镜像
        List<ThreadImage> userImages = threadImageRepository
            .findByUserIdOrderByCreatedAtDesc(userId);

        if (userImages.size() > maxImages) {
            // 删除超出限制的镜像 (按时间先后)
            List<ThreadImage> imagesToDelete = userImages.subList(maxImages, userImages.size());

            for (ThreadImage image : imagesToDelete) {
                try {
                    deleteThreadImage(image);
                    log.info("删除超出配额的镜像: user={}, image={}",
                            userId, image.getImageTag());
                } catch (Exception e) {
                    log.error("删除超出配额镜像失败: user={}, image={}",
                            userId, image.getImageTag(), e);
                }
            }
        }
    }

    /**
     * 镜像存储成本优化
     */
    @Scheduled(cron = "0 4 * * 0") // 每周日凌晨4点
    public void optimizeStorageCosts() {
        // 1. 分析镜像使用模式
        ImageUsageAnalysis analysis = analyzeImageUsagePatterns();

        // 2. 识别可以合并的相似镜像
        List<ImageMergeCandidate> mergeCandidates = findMergeCandidates(analysis);

        // 3. 执行镜像合并优化
        for (ImageMergeCandidate candidate : mergeCandidates) {
            try {
                mergeImages(candidate);
            } catch (Exception e) {
                log.error("镜像合并失败: {}", candidate, e);
            }
        }

        // 4. 压缩历史镜像
        compressHistoricalImages();
    }

    private void deleteImageFromAllServers(String imageTag) {
        List<DockerServer> servers = dockerServerService.getAllActiveServers();

        for (DockerServer server : servers) {
            try {
                dockerImageService.removeImage(server.getId(), imageTag);
            } catch (Exception e) {
                log.warn("删除服务器镜像失败: server={}, image={}",
                        server.getId(), imageTag, e);
            }
        }
    }
}
```

### 4.3 @meta 环境更新策略

```java
@Service
public class MetaEnvironmentUpdater {

    /**
     * @meta 环境定期更新 (30天周期)
     */
    @Scheduled(cron = "0 1 1 * * ?") // 每月1号凌晨1点
    public void updateMetaEnvironment() {
        try {
            // 1. 构建新的 @meta 环境
            String newMetaVersion = buildNewMetaEnvironment();

            // 2. 上传到 S3
            uploadMetaToS3(newMetaVersion);

            // 3. 通知所有 Docker Server 更新
            notifyServersToUpdateMeta(newMetaVersion);

            // 4. 更新版本记录
            updateMetaVersionRecord(newMetaVersion);

            log.info("@meta 环境更新完成: version={}", newMetaVersion);

        } catch (Exception e) {
            log.error("@meta 环境更新失败", e);
        }
    }

    /**
     * 用户重启容器时无感更新 @meta
     */
    @EventListener
    public void onContainerRestart(ContainerRestartEvent event) {
        DockerContainer container = event.getContainer();

        // 检查 @meta 环境是否需要更新
        if (isMetaEnvironmentOutdated(container.getDockerServerId())) {
            // 在容器重启时更新 @meta 环境
            updateServerMetaEnvironment(container.getDockerServerId());
        }
    }

    private String buildNewMetaEnvironment() {
        String buildDir = "/tmp/meta-build-" + System.currentTimeMillis();

        try {
            // 1. 创建构建目录
            Files.createDirectories(Paths.get(buildDir));

            // 2. 构建基础环境
            buildBaseEnvironment(buildDir);

            // 3. 安装最新的语言运行时
            installLanguageRuntimes(buildDir);

            // 4. 更新包管理器缓存
            updatePackageManagerCaches(buildDir);

            // 5. 打包环境
            String tarFile = buildDir + ".tar.gz";
            createTarArchive(buildDir, tarFile);

            return tarFile;

        } finally {
            // 清理构建目录
            FileUtils.deleteDirectory(new File(buildDir));
        }
    }

    private void notifyServersToUpdateMeta(String newVersion) {
        List<DockerServer> servers = dockerServerService.getAllActiveServers();

        for (DockerServer server : servers) {
            CompletableFuture.runAsync(() -> {
                updateServerMetaEnvironment(server.getId());
            });
        }
    }

    private void updateServerMetaEnvironment(String serverId) {
        try {
            String localMetaPath = "/cache/meta-" + serverId;
            String s3Key = "meta-environment/latest.tar.gz";

            // 下载最新的 @meta 环境
            s3Service.downloadAndExtract(s3Key, localMetaPath);

            // 更新本地缓存时间戳
            localCacheService.updateTimestamp(localMetaPath);

            log.info("服务器 @meta 环境更新完成: server={}", serverId);

        } catch (Exception e) {
            log.error("服务器 @meta 环境更新失败: server={}", serverId, e);
        }
    }
}
```

## 5. 数据库设计

### 5.1 Thread 镜像管理表

```sql
-- Thread 镜像表
CREATE TABLE thread_image (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    project_id BIGINT NOT NULL,
    issue_id BIGINT NULL,
    container_type ENUM('ROOT_THREAD', 'ISSUE_THREAD') NOT NULL,
    image_tag VARCHAR(255) NOT NULL,
    image_digest VARCHAR(255),
    image_size BIGINT,
    parent_image_tag VARCHAR(255),

    -- 用户信息
    user_id BIGINT NOT NULL,
    tenant_id BIGINT NOT NULL,

    -- 状态信息
    status ENUM('BUILDING', 'READY', 'PUSHING', 'AVAILABLE', 'FAILED') NOT NULL,
    build_log TEXT,

    -- 使用统计
    usage_count INT DEFAULT 0,
    last_used_at TIMESTAMP NULL,

    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 索引
    INDEX idx_project_issue (project_id, issue_id),
    INDEX idx_user_id (user_id),
    INDEX idx_image_tag (image_tag),
    INDEX idx_last_used (last_used_at),
    INDEX idx_status (status),

    UNIQUE KEY uk_project_issue_type (project_id, issue_id, container_type)
);

-- 镜像缓存表
CREATE TABLE image_cache (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    docker_server_id BIGINT NOT NULL,
    image_tag VARCHAR(255) NOT NULL,
    image_digest VARCHAR(255),
    cache_size BIGINT,

    -- 缓存状态
    status ENUM('PULLING', 'AVAILABLE', 'FAILED') NOT NULL,

    -- 使用统计
    hit_count INT DEFAULT 0,
    last_hit_at TIMESTAMP NULL,

    -- 时间戳
    cached_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 索引
    INDEX idx_server_image (docker_server_id, image_tag),
    INDEX idx_last_hit (last_hit_at),
    INDEX idx_status (status),

    UNIQUE KEY uk_server_image (docker_server_id, image_tag)
);

-- @meta 环境版本表
CREATE TABLE meta_environment_version (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    version VARCHAR(50) NOT NULL,
    s3_key VARCHAR(255) NOT NULL,
    size_bytes BIGINT,

    -- 版本信息
    description TEXT,
    changelog TEXT,

    -- 状态
    status ENUM('BUILDING', 'AVAILABLE', 'DEPRECATED') NOT NULL,

    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 索引
    INDEX idx_version (version),
    INDEX idx_status (status),

    UNIQUE KEY uk_version (version)
);

-- 用户镜像配额表
CREATE TABLE user_image_quota (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    user_level ENUM('FREE', 'BASIC', 'PRO', 'ENTERPRISE') NOT NULL,

    -- 配额限制
    max_thread_images INT NOT NULL,
    max_storage_gb INT NOT NULL,

    -- 当前使用量
    current_image_count INT DEFAULT 0,
    current_storage_gb DECIMAL(10,2) DEFAULT 0,

    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- 索引
    INDEX idx_user_id (user_id),
    INDEX idx_user_level (user_level),

    UNIQUE KEY uk_user_id (user_id)
);
```

## 6. 实施方案

### 6.1 分阶段实施计划

#### 阶段一：基础镜像化能力 (2周)

```java
// 1. 实现基础的镜像生命周期管理
@Component
public class Phase1Implementation {

    // 容器停止时自动创建镜像
    public void implementAutoImageCreation() {
        // - 监听容器停止事件
        // - 执行 docker commit
        // - 推送到镜像仓库
        // - 更新数据库记录
    }

    // 基础的镜像调度
    public void implementBasicImageScheduling() {
        // - 检查是否有可用镜像
        // - 优先使用已有镜像启动容器
        // - 降级到基础镜像
    }
}
```

#### 阶段二：@meta 环境分离 (2周)

```java
// 2. 实现 @meta 环境的 Overlay 注入
@Component
public class Phase2Implementation {

    // @meta 环境本地缓存
    public void implementMetaCaching() {
        // - S3 下载 @meta 环境
        // - 本地 SSD 缓存
        // - 定期更新机制
    }

    // Overlay 文件系统挂载
    public void implementOverlayMount() {
        // - 创建 Overlay 文件系统
        // - 只读挂载 @meta 环境
        // - 用户可写层管理
    }
}
```

#### 阶段三：智能调度优化 (2周)

```java
// 3. 实现基于镜像缓存的智能调度
@Component
public class Phase3Implementation {

    // 镜像缓存亲和性调度
    public void implementAffinityScheduling() {
        // - 分析镜像缓存情况
        // - 优先调度到有缓存的服务器
        // - 异步预拉取镜像
    }

    // 热点镜像管理
    public void implementHotImageManagement() {
        // - 分析镜像使用热度
        // - 预拉取热点镜像
        // - 智能缓存清理
    }
}
```

#### 阶段四：性能优化和监控 (2周)

```java
// 4. 实现性能监控和优化
@Component
public class Phase4Implementation {

    // 性能监控
    public void implementPerformanceMonitoring() {
        // - 容器启动时间监控
        // - 镜像拉取时间监控
        // - 缓存命中率统计
    }

    // 存储成本优化
    public void implementStorageOptimization() {
        // - 不活跃镜像清理
        // - 用户配额管理
        // - 存储成本分析
    }
}
```

### 6.2 配置管理

```yaml
# application.yml 镜像化配置
system:
  image:
    # 镜像仓库配置
    registry:
      # 生产环境使用 Amazon ECR
      url: ${IMAGE_REGISTRY_URL:123456789.dkr.ecr.us-west-2.amazonaws.com}
      username: ${ECR_USERNAME:AWS}
      password: ${ECR_PASSWORD}
      namespace: clacky-paas

    # 镜像构建配置
    build:
      timeout-seconds: 300
      concurrent-builds: 5
      auto-cleanup: true

    # 缓存配置
    cache:
      # 热点镜像预拉取
      hot-image-threshold: 10
      preload-enabled: true

      # 缓存清理
      cleanup-enabled: true
      inactive-days: 30

    # @meta 环境配置
    meta:
      s3-bucket: clacky-meta-environment
      update-interval-days: 30
      local-cache-path: /cache/meta

    # 用户配额配置
    quota:
      free-tier:
        max-images: 5
        max-storage-gb: 10
      basic-tier:
        max-images: 20
        max-storage-gb: 50
      pro-tier:
        max-images: 100
        max-storage-gb: 200
      enterprise-tier:
        max-images: -1  # 无限制
        max-storage-gb: -1
```

### 6.3 监控指标

```java
// 关键性能指标监控
@Component
public class ImagePerformanceMetrics {

    @Autowired
    private MeterRegistry meterRegistry;

    // 容器启动时间
    public void recordContainerStartTime(long startTimeMs) {
        Timer.Sample sample = Timer.start(meterRegistry);
        sample.stop(Timer.builder("container.start.time")
            .description("Container startup time")
            .register(meterRegistry));
    }

    // 镜像缓存命中率
    public void recordCacheHit(boolean hit) {
        Counter.builder("image.cache.hit")
            .tag("result", hit ? "hit" : "miss")
            .register(meterRegistry)
            .increment();
    }

    // 镜像拉取时间
    public void recordImagePullTime(String imageTag, long pullTimeMs) {
        Timer.builder("image.pull.time")
            .tag("image", imageTag)
            .register(meterRegistry)
            .record(pullTimeMs, TimeUnit.MILLISECONDS);
    }

    // 存储使用量
    public void recordStorageUsage(long storageBytes) {
        Gauge.builder("image.storage.usage")
            .description("Total image storage usage")
            .register(meterRegistry, this, obj -> storageBytes);
    }
}
```

## 7. 预期效果

### 7.1 性能提升

- **并发能力**：从 500 并发提升到 10000+ 并发
- **启动时间**：从 50s-120s 降低到 5s-15s
- **超时率**：从 40%+ 降低到 <5%
- **资源利用率**：NFS 存储压力降低 70%+

### 7.2 成本优化

- **存储成本**：通过智能清理和配额管理，降低 30%+ 存储成本
- **网络成本**：本地缓存减少重复拉取，降低网络传输成本
- **运维成本**：自动化管理减少人工干预

### 7.3 用户体验

- **无感知升级**：用户无需改变使用习惯
- **更快响应**：容器启动速度显著提升
- **更高可用性**：分布式架构提高系统稳定性

#### 2.2.2 多层镜像优化策略

```java
public class LayeredImageStrategy {
    
    /**
     * 生成优化的多层 Dockerfile
     */
    public String generateOptimizedDockerfile(CodeZoneAnalysis analysis) {
        StringBuilder dockerfile = new StringBuilder();
        
        // 第一层：基础环境 (复用现有镜像)
        dockerfile.append("FROM ").append(analysis.getBaseEnvironment().getImage()).append("\n\n");
        
        // 第二层：系统依赖 (缓存友好)
        dockerfile.append("# 系统依赖层\n");
        dockerfile.append("RUN apt-get update && apt-get install -y \\\n");
        analysis.getSystemPackages().forEach(pkg -> 
            dockerfile.append("    ").append(pkg).append(" \\\n"));
        dockerfile.append("    && rm -rf /var/lib/apt/lists/*\n\n");
        
        // 第三层：语言包管理器依赖 (变化较少)
        if (analysis.hasPackageManagerFiles()) {
            dockerfile.append("# 包管理器依赖层\n");
            dockerfile.append("COPY package*.json requirements.txt go.mod go.sum ./\n");
            dockerfile.append("RUN ").append(generateInstallCommand(analysis)).append("\n\n");
        }
        
        // 第四层：用户代码 (变化最频繁)
        dockerfile.append("# 用户代码层\n");
        dockerfile.append("COPY source/ /home/<USER>/app/\n");
        dockerfile.append("WORKDIR /home/<USER>/app\n\n");
        
        // 第五层：中间件配置
        if (!analysis.getMiddlewares().isEmpty()) {
            dockerfile.append("# 中间件配置层\n");
            generateMiddlewareConfig(dockerfile, analysis.getMiddlewares());
        }
        
        // 第六层：运行时配置
        dockerfile.append("# 运行时配置\n");
        dockerfile.append("USER runner\n");
        dockerfile.append("EXPOSE 8080\n");
        dockerfile.append("CMD [\"/agent/agent\"]\n");
        
        return dockerfile.toString();
    }
}
```

### 2.3 增量构建机制

```java
@Service
public class IncrementalImageBuilder {
    
    /**
     * 基于变更检测的增量构建
     */
    public ImageBuildResult incrementalBuild(CodeZone codeZone, String previousImageTag) {
        // 1. 检测变更
        ChangeDetection changes = detectChanges(codeZone, previousImageTag);
        
        if (changes.isEmpty()) {
            return ImageBuildResult.noChanges(previousImageTag);
        }
        
        // 2. 确定需要重建的层
        List<ImageLayer> layersToRebuild = determineLayers(changes);
        
        // 3. 执行增量构建
        return buildIncrementally(codeZone, layersToRebuild, previousImageTag);
    }
    
    /**
     * 检测 CodeZone 变更
     */
    private ChangeDetection detectChanges(CodeZone codeZone, String previousImageTag) {
        ChangeDetection detection = new ChangeDetection();
        
        // 检测源代码变更
        String sourceHash = calculateDirectoryHash(
            codeZone.getRootPath() + Constant.CODE_ZONE_SOURCE_PATH);
        detection.setSourceChanged(!sourceHash.equals(getPreviousSourceHash(previousImageTag)));
        
        // 检测依赖变更
        String depHash = calculateDependencyHash(
            codeZone.getRootPath() + Constant.CODE_ZONE_DEPENDENCY_PATH);
        detection.setDependencyChanged(!depHash.equals(getPreviousDependencyHash(previousImageTag)));
        
        // 检测中间件配置变更
        String middlewareHash = calculateMiddlewareHash(codeZone.getMiddlewareConfigList());
        detection.setMiddlewareChanged(!middlewareHash.equals(getPreviousMiddlewareHash(previousImageTag)));
        
        return detection;
    }
}
```

## 3. 实现方案

### 3.1 数据库设计

```sql
-- CodeZone 镜像表
CREATE TABLE codezone_image (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    codezone_id BIGINT NOT NULL,
    image_tag VARCHAR(255) NOT NULL,
    image_digest VARCHAR(255),
    build_status ENUM('BUILDING', 'SUCCESS', 'FAILED') NOT NULL,
    build_log TEXT,
    source_hash VARCHAR(64),
    dependency_hash VARCHAR(64),
    middleware_hash VARCHAR(64),
    image_size BIGINT,
    build_duration INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_codezone_id (codezone_id),
    INDEX idx_image_tag (image_tag),
    INDEX idx_build_status (build_status)
);

-- 镜像层缓存表
CREATE TABLE image_layer_cache (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    layer_type ENUM('BASE', 'SYSTEM', 'DEPENDENCY', 'SOURCE', 'MIDDLEWARE') NOT NULL,
    content_hash VARCHAR(64) NOT NULL,
    layer_digest VARCHAR(255),
    layer_size BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_content_hash (content_hash),
    INDEX idx_layer_type (layer_type)
);
```

### 3.2 API 接口设计

```java
@RestController
@RequestMapping("/api/codezone/image")
public class CodeZoneImageController {
    
    /**
     * 构建 CodeZone 镜像
     */
    @PostMapping("/{codeZoneId}/build")
    public ResponseEntity<ImageBuildResponse> buildImage(
            @PathVariable Long codeZoneId,
            @RequestBody ImageBuildRequest request) {
        
        ImageBuildOptions options = ImageBuildOptions.builder()
            .tag(request.getTag())
            .incremental(request.isIncremental())
            .optimize(request.isOptimize())
            .registry(request.getRegistry())
            .build();
            
        ImageBuildResult result = imageBuilder.buildImage(codeZoneId, options);
        
        return ResponseEntity.ok(ImageBuildResponse.from(result));
    }
    
    /**
     * 获取镜像构建状态
     */
    @GetMapping("/{codeZoneId}/build/{buildId}/status")
    public ResponseEntity<BuildStatusResponse> getBuildStatus(
            @PathVariable Long codeZoneId,
            @PathVariable String buildId) {
        
        BuildStatus status = imageBuilder.getBuildStatus(buildId);
        return ResponseEntity.ok(BuildStatusResponse.from(status));
    }
    
    /**
     * 推送镜像到仓库
     */
    @PostMapping("/{codeZoneId}/push")
    public ResponseEntity<PushResponse> pushImage(
            @PathVariable Long codeZoneId,
            @RequestBody PushRequest request) {
        
        PushResult result = imageManager.pushImage(
            request.getImageTag(), 
            request.getRegistry()
        );
        
        return ResponseEntity.ok(PushResponse.from(result));
    }
}
```

### 3.3 配置管理

```yaml
# application.yml 中的镜像化配置
system:
  image:
    # 镜像构建配置
    build:
      # 构建超时时间 (分钟)
      timeout: 30
      # 并发构建数量
      concurrent-builds: 3
      # 构建工作目录
      work-dir: /tmp/codezone-builds
      # 是否启用层缓存
      layer-cache-enabled: true
      
    # 镜像仓库配置
    registry:
      # 默认仓库地址
      default-url: registry.clacky.ai
      # 认证信息
      username: ${REGISTRY_USERNAME}
      password: ${REGISTRY_PASSWORD}
      # 镜像命名规则
      naming-pattern: "clacky/codezone-{tenantId}-{codeZoneId}:{version}"
      
    # 优化配置
    optimization:
      # 是否启用多阶段构建
      multi-stage-build: true
      # 是否压缩镜像层
      compress-layers: true
      # 基础镜像缓存时间 (小时)
      base-image-cache-ttl: 24
```
