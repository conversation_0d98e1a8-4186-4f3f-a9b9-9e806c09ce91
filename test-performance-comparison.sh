#!/bin/bash

# CodeZone 镜像化性能对比测试
# 对比基础镜像启动 vs 缓存镜像启动的性能差异

set -e

echo "🚀 CodeZone 镜像化性能对比测试"
echo "=================================="

# 测试配置
BASE_IMAGE="ubuntu:20.04"
TEST_ITERATIONS=3
RESULTS_FILE="performance_results_$(date +%Y%m%d_%H%M%S).txt"

# 初始化结果文件
echo "CodeZone 镜像化性能测试结果" > "$RESULTS_FILE"
echo "测试时间: $(date)" >> "$RESULTS_FILE"
echo "测试迭代次数: $TEST_ITERATIONS" >> "$RESULTS_FILE"
echo "=================================" >> "$RESULTS_FILE"

# 性能测试函数
test_base_image_startup() {
    local iteration=$1
    echo "  📊 第 $iteration 次测试 - 基础镜像启动..."
    
    local start_time=$(date +%s)
    local start_ms=$(date +%3N)
    local container_name="perf-base-$iteration-$(date +%s)"

    # 创建并启动容器
    local container_id=$(docker run -d --name "$container_name" "$BASE_IMAGE" sleep 30)

    # 等待容器完全启动
    while [ "$(docker inspect -f '{{.State.Running}}' "$container_id")" != "true" ]; do
        sleep 0.1
    done

    local end_time=$(date +%s)
    local end_ms=$(date +%3N)
    local startup_time=$((end_time - start_time))
    
    echo "    ⏱️  基础镜像启动时间: ${startup_time}ms"
    echo "基础镜像启动 - 第${iteration}次: ${startup_time}ms" >> "$RESULTS_FILE"
    
    # 清理
    docker rm -f "$container_id" > /dev/null 2>&1
    
    echo "$startup_time"
}

test_cached_image_startup() {
    local iteration=$1
    local cached_image=$2
    echo "  📊 第 $iteration 次测试 - 缓存镜像启动..."
    
    local start_time=$(date +%s%3N)
    local container_name="perf-cached-$iteration-$(date +%s)"
    
    # 基于缓存镜像创建并启动容器
    local container_id=$(docker run -d --name "$container_name" "$cached_image" sleep 30)
    
    # 等待容器完全启动
    while [ "$(docker inspect -f '{{.State.Running}}' "$container_id")" != "true" ]; do
        sleep 0.1
    done
    
    local end_time=$(date +%s%3N)
    local startup_time=$((end_time - start_time))
    
    echo "    ⏱️  缓存镜像启动时间: ${startup_time}ms"
    echo "缓存镜像启动 - 第${iteration}次: ${startup_time}ms" >> "$RESULTS_FILE"
    
    # 清理
    docker rm -f "$container_id" > /dev/null 2>&1
    
    echo "$startup_time"
}

create_cached_image() {
    echo "🔧 创建缓存镜像..."
    local base_container_name="cache-builder-$(date +%s)"
    local cached_image_tag="clacky/cached-codezone:$(date +%s)"
    
    # 创建基础容器并添加开发环境
    echo "  📦 创建基础容器..."
    local container_id=$(docker run -d --name "$base_container_name" "$BASE_IMAGE" sleep 60)
    
    # 模拟 CodeZone 环境设置
    echo "  🛠️  设置开发环境..."
    docker exec "$container_id" apt-get update > /dev/null 2>&1
    docker exec "$container_id" apt-get install -y curl wget git > /dev/null 2>&1
    docker exec "$container_id" mkdir -p /home/<USER>/app
    docker exec "$container_id" mkdir -p /home/<USER>/.cache
    docker exec "$container_id" sh -c 'echo "#!/bin/bash\necho \"CodeZone Ready!\"" > /home/<USER>/start.sh'
    docker exec "$container_id" chmod +x /home/<USER>/start.sh
    
    # 停止容器
    docker stop "$container_id" > /dev/null
    
    # 创建镜像
    echo "  🏗️  创建缓存镜像..."
    docker commit "$container_id" "$cached_image_tag" > /dev/null
    
    # 清理基础容器
    docker rm "$container_id" > /dev/null
    
    echo "  ✅ 缓存镜像创建完成: $cached_image_tag"
    echo "$cached_image_tag"
}

calculate_statistics() {
    local test_type=$1
    shift
    local times=("$@")
    
    local sum=0
    local min=${times[0]}
    local max=${times[0]}
    
    for time in "${times[@]}"; do
        sum=$((sum + time))
        if [ "$time" -lt "$min" ]; then
            min=$time
        fi
        if [ "$time" -gt "$max" ]; then
            max=$time
        fi
    done
    
    local avg=$((sum / ${#times[@]}))
    
    echo ""
    echo "📈 $test_type 统计结果:"
    echo "  平均时间: ${avg}ms"
    echo "  最短时间: ${min}ms"
    echo "  最长时间: ${max}ms"
    echo "  测试次数: ${#times[@]}"
    
    echo "" >> "$RESULTS_FILE"
    echo "$test_type 统计结果:" >> "$RESULTS_FILE"
    echo "  平均时间: ${avg}ms" >> "$RESULTS_FILE"
    echo "  最短时间: ${min}ms" >> "$RESULTS_FILE"
    echo "  最长时间: ${max}ms" >> "$RESULTS_FILE"
    echo "  测试次数: ${#times[@]}" >> "$RESULTS_FILE"
    
    echo "$avg"
}

# 主测试流程
echo "📋 准备测试环境..."

# 确保基础镜像存在
echo "  🔍 拉取基础镜像..."
docker pull "$BASE_IMAGE" > /dev/null 2>&1

# 创建缓存镜像
cached_image=$(create_cached_image)

echo ""
echo "🧪 开始性能对比测试..."
echo "=================================="

# 基础镜像启动时间测试
echo "📊 测试基础镜像启动性能..."
base_times=()
for i in $(seq 1 $TEST_ITERATIONS); do
    time=$(test_base_image_startup $i)
    base_times+=($time)
    sleep 1  # 避免资源竞争
done

# 缓存镜像启动时间测试
echo ""
echo "📊 测试缓存镜像启动性能..."
cached_times=()
for i in $(seq 1 $TEST_ITERATIONS); do
    time=$(test_cached_image_startup $i "$cached_image")
    cached_times+=($time)
    sleep 1  # 避免资源竞争
done

# 计算统计结果
echo ""
echo "📈 性能分析结果"
echo "=================================="

base_avg=$(calculate_statistics "基础镜像启动" "${base_times[@]}")
cached_avg=$(calculate_statistics "缓存镜像启动" "${cached_times[@]}")

# 计算性能提升
improvement_ms=$((base_avg - cached_avg))
if [ "$base_avg" -gt 0 ]; then
    improvement_percent=$(( (improvement_ms * 100) / base_avg ))
else
    improvement_percent=0
fi

echo ""
echo "🎯 性能提升分析:"
echo "  时间节省: ${improvement_ms}ms"
echo "  性能提升: ${improvement_percent}%"

echo "" >> "$RESULTS_FILE"
echo "性能提升分析:" >> "$RESULTS_FILE"
echo "  基础镜像平均启动时间: ${base_avg}ms" >> "$RESULTS_FILE"
echo "  缓存镜像平均启动时间: ${cached_avg}ms" >> "$RESULTS_FILE"
echo "  时间节省: ${improvement_ms}ms" >> "$RESULTS_FILE"
echo "  性能提升: ${improvement_percent}%" >> "$RESULTS_FILE"

# 清理缓存镜像
echo ""
echo "🧹 清理测试资源..."
docker rmi "$cached_image" > /dev/null 2>&1

echo ""
echo "✅ 性能测试完成！"
echo "📄 详细结果已保存到: $RESULTS_FILE"

# 显示结果摘要
echo ""
echo "📊 测试结果摘要:"
echo "=================================="
echo "基础镜像平均启动时间: ${base_avg}ms"
echo "缓存镜像平均启动时间: ${cached_avg}ms"
echo "性能提升: ${improvement_percent}%"

if [ "$improvement_percent" -gt 20 ]; then
    echo "🎉 镜像缓存显著提升了启动性能！"
elif [ "$improvement_percent" -gt 0 ]; then
    echo "✅ 镜像缓存带来了性能提升。"
else
    echo "⚠️  镜像缓存未带来明显性能提升，可能需要优化。"
fi

echo ""
echo "💡 建议:"
echo "- 在生产环境中使用镜像缓存可以显著提升用户体验"
echo "- 考虑实施智能预拉取策略以进一步优化性能"
echo "- 定期清理不活跃的镜像以节省存储空间"
