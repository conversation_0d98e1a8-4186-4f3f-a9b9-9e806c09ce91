services:
  mysql:
    image: mysql:8.0
    ports:
      - 3306:3306
    environment:
      MYSQL_ROOT_PASSWORD: rd123456
      MYSQL_ROOT_HOST: '%'
    volumes: []
    command: >
      /bin/bash -c "apt-get update && apt-get install -y curl && \
      curl -o /docker-entrypoint-initdb.d/paas_develop.schema.sql https://clacky.s3.us-east-1.amazonaws.com/work/dev-env-assets/paas_develop.schema.sql && \
      docker-entrypoint.sh mysqld --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci"

  redis:
    image: redis:6.0.6-alpine
    ports:
      - 6379:6379
    command: redis-server --requirepass rd123456
    environment:
      REDIS_PASSWORD: rd123456

  rabbitmq:
    image: rabbitmq:3.9.9-management-alpine
    ports:
      - 5672:5672
      - 15672:15672
    environment:
      RABBITMQ_DEFAULT_USER: agent
      RABBITMQ_DEFAULT_PASS: d42agent
      RABBITMQ_DEFAULT_VHOST: dev

  kong-database:
    image: postgres:14
    restart: always
    networks:
      - kong-net
    environment:
      POSTGRES_USER: root
      POSTGRES_DB: kong
      POSTGRES_PASSWORD: 123456
      TZ: Asia/Shanghai
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"

  # 为kong数据库初始化
  kong-migration:
    image: kong:2.6.0
    command: "kong migrations bootstrap"
    networks:
      - kong-net
    restart: on-failure
    environment:
      KONG_PG_HOST: kong-database
      KONG_PG_USER: root
      KONG_PG_PASSWORD: 123456
      KONG_PG_DATABASE: kong
    depends_on:
      - kong-database

  kong:
    image: kong:2.6.0
    restart: always
    networks:
      - kong-net
    environment:
      KONG_PG_HOST: kong-database
      KONG_PG_USER: root
      KONG_PG_PASSWORD: 123456
      KONG_PG_DATABASE: kong
      KONG_PROXY_ACCESS_LOG: /dev/stdout
      KONG_ADMIN_ACCESS_LOG: /dev/stdout
      KONG_PROXY_ERROR_LOG: /dev/stderr
      KONG_ADMIN_ERROR_LOG: /dev/stderr
      KONG_ADMIN_LISTEN: 0.0.0.0:8001
    depends_on:
      - kong-migration
    ports:
      - "8080:8000"
      - "8443:8443"
      - "8001:8001"
      - "8444:8444"

  # 开发环境的Manager服务 - 支持热重载
  manager-dev:
    build:
      context: .
      dockerfile: d42paas_manager/Dockerfile-dev
    container_name: manager-app-dev
    ports:
      - "8000:8000"
    volumes:
      # 挂载源码目录以支持热重载
      - ./d42paas_manager/src:/app/d42paas_manager/src
      - ./d42paas_common/src:/app/d42paas_common/src
      # 挂载Maven本地仓库以加速依赖下载
      - ~/.m2:/home/<USER>/.m2
    environment:
      SPRING_PROFILES_ACTIVE: local
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PORT: 6379
      SPRING_REDIS_PASSWORD: ""
      SPRING_DATASOURCE_URL: **************************************************************************************************************************************************
      SPRING_RABBITMQ_HOST: rabbitmq
      SPRING_RABBITMQ_PORT: 5672
      SPRING_RABBITMQ_USERNAME: agent
      SPRING_RABBITMQ_PASSWORD: d42agent
      SPRING_RABBITMQ_VIRTUAL_HOST: dev
    depends_on:
      - mysql
      - redis
      - rabbitmq
    networks:
      - default
      - kong-net

networks:
  kong-net:
    driver: bridge