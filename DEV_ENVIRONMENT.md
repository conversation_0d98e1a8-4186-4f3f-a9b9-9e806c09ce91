# 开发环境热重载指南

本文档介绍如何使用新的开发环境热重载功能，大幅提升开发调试效率。

## 🚀 快速开始

### 1. 启动开发环境

```bash
# 完整初始化开发环境（首次使用）
make dev

# 或者分步骤启动
make dev-build    # 构建开发镜像
make dev-start    # 启动所有服务
```

### 2. 查看服务状态

启动完成后，您可以访问以下服务：

- **Manager服务**: http://localhost:8000
- **MySQL数据库**: localhost:3306 (用户: root, 密码: rd123456)
- **Redis缓存**: localhost:6379 (密码: rd123456)
- **RabbitMQ**: localhost:5672 (用户: agent, 密码: d42agent)
- **RabbitMQ管理界面**: http://localhost:15672
- **Kong代理**: http://localhost:8080
- **Kong管理API**: http://localhost:8001

## 🔥 热重载功能

### 自动重载机制

当您修改以下类型的文件时，系统会自动：
1. 检测文件变化
2. 重新编译项目
3. 重启Java应用
4. 显示编译和启动日志

**支持的文件类型**：
- `.java` - Java源码文件
- `.xml` - Maven配置和MyBatis映射文件
- `.yml/.yaml` - Spring配置文件
- `.properties` - 属性配置文件

**监控目录**：
- `d42paas_manager/src/` - Manager模块源码
- `d42paas_common/src/` - 公共模块源码

### 实时日志查看

```bash
# 查看Manager服务实时日志
make dev-logs

# 查看所有服务日志
docker-compose -f docker-compose.dev.yml logs -f
```

## 🛠️ 常用命令

### 基本操作

```bash
# 启动开发环境
make dev-start

# 停止开发环境
make dev-stop

# 重启Manager服务
make dev-restart

# 查看实时日志
make dev-logs

# 进入开发容器
make dev-shell
```

### 调试操作

```bash
# 重新构建开发镜像
make dev-build

# 清理开发容器
make dev-clean

# 查看容器状态
docker-compose -f docker-compose.dev.yml ps
```

## 📁 目录结构

```
clacky-ai-paas-backend/
├── d42paas_manager/
│   ├── Dockerfile-dev              # 开发环境Dockerfile
│   └── scripts/
│       ├── dev-hotreload.sh        # 热重载脚本（inotify版本）
│       └── dev-hotreload-simple.sh # 热重载脚本（轮询版本）
├── docker-compose.dev.yml          # 开发环境Docker Compose配置
└── DEV_ENVIRONMENT.md              # 本文档
```

## ⚙️ 技术实现

### 热重载原理

1. **文件监控**: 使用`inotify-tools`监控源码目录的文件变化
2. **自动编译**: 检测到变化后自动执行`mvn clean package`
3. **进程管理**: 优雅停止旧进程，启动新进程
4. **日志聚合**: 实时显示编译和运行日志

### 性能优化

- **Maven依赖缓存**: 挂载本地`.m2`目录，避免重复下载依赖
- **增量编译**: Maven自动检测变化的文件进行增量编译
- **容器复用**: 开发容器保持运行，只重启Java进程
- **并行日志**: 编译和运行日志并行显示

## 🔧 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   lsof -i :8000
   
   # 停止冲突的服务
   make dev-stop
   ```

2. **编译失败**
   ```bash
   # 查看详细编译日志
   docker-compose -f docker-compose.dev.yml exec manager-dev cat /tmp/build.log
   ```

3. **热重载不工作**
   ```bash
   # 检查文件监控进程
   docker-compose -f docker-compose.dev.yml exec manager-dev ps aux | grep inotify
   
   # 重启Manager服务
   make dev-restart
   ```

4. **数据库连接失败**
   ```bash
   # 检查MySQL服务状态
   docker-compose -f docker-compose.dev.yml ps mysql
   
   # 重新初始化数据库
   make dev-stop
   make dev
   ```

### 日志位置

- **应用日志**: `/tmp/d42paas-manager.log`
- **编译日志**: `/tmp/build.log`
- **进程ID**: `/tmp/d42paas-manager.pid`

## 🆚 对比传统开发方式

| 特性 | 传统方式 | 热重载方式 |
|------|----------|------------|
| 代码修改后 | 手动重新构建Docker镜像 | 自动重新编译和重启 |
| 构建时间 | 30-60秒 | 5-15秒 |
| 操作步骤 | 停止容器 → 构建镜像 → 启动容器 | 保存文件即可 |
| 开发体验 | 中断式 | 连续式 |
| 资源消耗 | 高（重建镜像） | 低（只重启进程） |

## 📝 最佳实践

1. **保持开发环境运行**: 一次启动，持续开发
2. **使用实时日志**: `make dev-logs`查看应用状态
3. **分模块开发**: 修改common模块会触发manager重启
4. **定期清理**: 开发完成后执行`make dev-stop`释放资源
5. **版本控制**: 不要提交临时的调试代码

## 🔄 从生产环境切换

如果您之前使用生产环境的构建方式：

```bash
# 停止生产环境
make stop

# 启动开发环境
make dev
```

如果需要切换回生产环境：

```bash
# 停止开发环境
make dev-stop

# 启动生产环境
make init
```

---

**享受高效的开发体验！** 🎉
