---
description: 
globs: 
alwaysApply: false
---
\
# Project Structure Guide

The main entry point for the `d42paas_manager` module is [PaaSManager.java](mdc:d42paas_manager/src/main/java/com/dao42/paas/PaaSManager.java).

Key configuration files and directories include:
- Maven project configuration: [pom.xml](mdc:d42paas_manager/pom.xml)
- Application resources: [resources](mdc:d42paas_manager/src/main/resources)

Other important modules in the project are:
- [d42paas_common](mdc:d42paas_common)
- [d42paas_demo](mdc:d42paas_demo)
- [d42paas_admin](mdc:d42paas_admin)
