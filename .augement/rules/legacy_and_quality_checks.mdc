---
description: 
globs: 
alwaysApply: false
---
# Java Monorepo：遗留代码、质量和环境配置指南

本指南侧重于与遗留代码、Java 版本、代码质量和特定环境配置相关的方面。

## 1. JDK 版本和兼容性

- **确认 JDK 版本**：检查 `[pom.xml](mdc:pom.xml)`（或其他构建文件）中的编译器设置。
    ```xml
    <properties>
        <java.version>1.8</java.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
    </properties>
    ```
    或通过编译器插件配置：
    ```xml
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
    ```
- **遗留代码识别（Java 8 或更早版本）**：
    - 查找旧 API 的使用情况（例如，使用 `java.util.Date` 而不是 `java.time`，在适当的地方使用匿名内部类而不是 lambda 表达式）。
    - 检查可能存在已知漏洞或与较新 Java 版本存在兼容性问题的旧库版本。
- **Spring/Hibernate 兼容性**：如果识别出旧版本的 Spring 或 Hibernate，请注意它们与目标 JDK（例如 JDK 1.8）的兼容性以及潜在问题。

## 2. 技术债务和质量评估

- **过时的依赖项**：根据当前稳定版本检查 `[pom.xml](mdc:pom.xml)` 文件中的依赖项版本。在父 POM 中查找 `<dependencyManagement>`，它可能集中管理版本。
- **代码质量与标准**：（手动或工具辅助识别）
    - 不一致的格式。
    - 缺少注释或注释过时。
    - 可以重构的复杂方法/类。
    - 静态分析工具配置（例如 Checkstyle、PMD、FindBugs）的存在可以表明现有标准，但如果报告陈旧或被忽略，也表明需要审查的领域。
- **测试覆盖率和冗余**：检查 `src/test/java` 目录。
    - 查找单元测试（例如 JUnit、TestNG）的存在和性质。
    - 虽然难以完全自动化，但要注意冗余或过时测试用例的潜力。
- **未使用的依赖项**：像 `mvn dependency:analyze` 这样的工具可以帮助识别模块 `[pom.xml](mdc:pom.xml)` 中已声明但未使用的依赖项。
- **代码年龄**：使用版本控制历史（例如 `git log`）来了解文件/模块的最后修改时间。这可以表明是积极维护的代码还是停滞的代码。

## 3. 特定环境配置

- **区分测试环境与生产环境**：应用程序配置（例如，在 `src/main/resources/application.properties`、`src/main/resources/application.yml`中）通常使用配置文件（例如 Spring Profiles）来管理差异。
    - `application-dev.properties`、`application-prod.properties`、`application-test.properties`
    - 可以通过系统属性、环境变量或在 `application.properties` (`spring.profiles.active=dev`) 中激活配置文件。
- **Dockerfile/容器化**：如果存在 `[Dockerfile](mdc:Dockerfile)` 或 `docker-compose.yml` 文件，请检查它们如何将特定于环境的设置传递给容器（例如，环境变量、配置文件的卷挂载）。

## 4. 注释中的设计决策

- **保留历史背景**：在分析代码时，请注意解释*为什么*做出某个设计选择的注释，尤其是在较旧的代码部分。这些对于理解架构演进非常有价值。

## 通用工作流程：

- 从高层次开始，识别 Java 版本和构建配置。
- 然后深入研究特定模块，尤其是那些被识别为核心或遗留模块，进行更深入的质量和依赖项检查。
