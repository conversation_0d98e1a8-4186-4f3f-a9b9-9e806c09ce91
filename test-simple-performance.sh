#!/bin/bash

# 简化的 CodeZone 镜像化性能测试
# 测试容器创建、镜像生成和基于镜像启动的性能

set -e

echo "🚀 CodeZone 镜像化简化性能测试"
echo "=================================="

# 清理函数
cleanup() {
    echo "🧹 清理测试资源..."
    docker ps -a --format "{{.Names}}" | grep "test-perf-" | xargs -r docker rm -f > /dev/null 2>&1
    docker images --format "{{.Repository}}:{{.Tag}}" | grep "clacky/test-perf" | xargs -r docker rmi -f > /dev/null 2>&1
}

# 设置清理陷阱
trap cleanup EXIT

# 测试1: 基础容器创建和启动
echo "📊 测试1: 基础容器创建和启动"
echo "--------------------------------"

start_time=$(date +%s)
container1_name="test-perf-base-$(date +%s)"
container1_id=$(docker run -d --name "$container1_name" ubuntu:20.04 sleep 60)
echo "容器ID: $container1_id"

# 等待容器启动
sleep 2
if [ "$(docker inspect -f '{{.State.Running}}' "$container1_id")" = "true" ]; then
    end_time=$(date +%s)
    base_startup_time=$((end_time - start_time))
    echo "✅ 基础容器启动时间: ${base_startup_time}秒"
else
    echo "❌ 基础容器启动失败"
    exit 1
fi

# 测试2: 在容器中模拟开发环境设置
echo ""
echo "📊 测试2: 模拟开发环境设置"
echo "--------------------------------"

start_time=$(date +%s)
echo "  🔧 安装开发工具..."
docker exec "$container1_id" apt-get update > /dev/null 2>&1
docker exec "$container1_id" apt-get install -y curl wget > /dev/null 2>&1

echo "  📁 创建项目结构..."
docker exec "$container1_id" mkdir -p /home/<USER>/app
docker exec "$container1_id" mkdir -p /home/<USER>/.cache

echo "  📝 创建项目文件..."
docker exec "$container1_id" sh -c 'echo "console.log(\"Hello CodeZone!\");" > /home/<USER>/app/index.js'
docker exec "$container1_id" sh -c 'echo "{\"name\": \"test-app\", \"version\": \"1.0.0\"}" > /home/<USER>/app/package.json'
docker exec "$container1_id" sh -c 'echo "# Test Project\nThis is a test project." > /home/<USER>/app/README.md'

end_time=$(date +%s)
setup_time=$((end_time - start_time))
echo "✅ 开发环境设置时间: ${setup_time}秒"

# 测试3: 停止容器并创建镜像
echo ""
echo "📊 测试3: 容器停止和镜像创建"
echo "--------------------------------"

start_time=$(date +%s)
echo "  ⏹️  停止容器..."
docker stop "$container1_id" > /dev/null

echo "  🏗️  创建镜像..."
image_tag="clacky/test-perf:$(date +%s)"
image_id=$(docker commit "$container1_id" "$image_tag")
echo "镜像ID: $image_id"

end_time=$(date +%s)
image_creation_time=$((end_time - start_time))
echo "✅ 镜像创建时间: ${image_creation_time}秒"

# 验证镜像
image_size=$(docker images "$image_tag" --format "{{.Size}}")
echo "📦 镜像大小: $image_size"

# 测试4: 基于镜像快速启动新容器
echo ""
echo "📊 测试4: 基于镜像的快速启动"
echo "--------------------------------"

start_time=$(date +%s)
container2_name="test-perf-cached-$(date +%s)"
container2_id=$(docker run -d --name "$container2_name" "$image_tag" sleep 60)
echo "新容器ID: $container2_id"

# 等待容器启动
sleep 2
if [ "$(docker inspect -f '{{.State.Running}}' "$container2_id")" = "true" ]; then
    end_time=$(date +%s)
    cached_startup_time=$((end_time - start_time))
    echo "✅ 缓存镜像启动时间: ${cached_startup_time}秒"
else
    echo "❌ 缓存镜像容器启动失败"
    exit 1
fi

# 测试5: 验证镜像内容完整性
echo ""
echo "📊 测试5: 验证镜像内容完整性"
echo "--------------------------------"

echo "  🔍 检查项目文件..."
if docker exec "$container2_id" test -f /home/<USER>/app/index.js; then
    echo "  ✅ index.js 存在"
else
    echo "  ❌ index.js 不存在"
    exit 1
fi

if docker exec "$container2_id" test -f /home/<USER>/app/package.json; then
    echo "  ✅ package.json 存在"
else
    echo "  ❌ package.json 不存在"
    exit 1
fi

echo "  🔍 检查安装的工具..."
if docker exec "$container2_id" which curl > /dev/null 2>&1; then
    echo "  ✅ curl 已安装"
else
    echo "  ❌ curl 未安装"
    exit 1
fi

if docker exec "$container2_id" which wget > /dev/null 2>&1; then
    echo "  ✅ wget 已安装"
else
    echo "  ❌ wget 未安装"
    exit 1
fi

# 测试6: 多容器并发启动测试
echo ""
echo "📊 测试6: 多容器并发启动测试"
echo "--------------------------------"

echo "  🚀 启动3个并发容器..."
start_time=$(date +%s)

container3_id=$(docker run -d --name "test-perf-concurrent1-$(date +%s)" "$image_tag" sleep 30) &
container4_id=$(docker run -d --name "test-perf-concurrent2-$(date +%s)" "$image_tag" sleep 30) &
container5_id=$(docker run -d --name "test-perf-concurrent3-$(date +%s)" "$image_tag" sleep 30) &

# 等待所有后台任务完成
wait

end_time=$(date +%s)
concurrent_startup_time=$((end_time - start_time))
echo "✅ 3个并发容器启动时间: ${concurrent_startup_time}秒"

# 性能分析和总结
echo ""
echo "📈 性能分析结果"
echo "=================================="

echo "🕐 时间统计:"
echo "  基础容器启动: ${base_startup_time}秒"
echo "  开发环境设置: ${setup_time}秒"
echo "  镜像创建: ${image_creation_time}秒"
echo "  缓存镜像启动: ${cached_startup_time}秒"
echo "  并发启动(3个): ${concurrent_startup_time}秒"

total_first_time=$((base_startup_time + setup_time))
echo ""
echo "📊 性能对比:"
echo "  首次完整启动: ${total_first_time}秒 (容器启动 + 环境设置)"
echo "  后续快速启动: ${cached_startup_time}秒 (基于镜像)"

if [ "$total_first_time" -gt "$cached_startup_time" ]; then
    time_saved=$((total_first_time - cached_startup_time))
    if [ "$total_first_time" -gt 0 ]; then
        improvement_percent=$(( (time_saved * 100) / total_first_time ))
    else
        improvement_percent=0
    fi
    echo "  时间节省: ${time_saved}秒"
    echo "  性能提升: ${improvement_percent}%"
else
    echo "  性能提升: 0% (可能需要更复杂的环境设置才能看到明显差异)"
fi

echo ""
echo "💾 存储统计:"
echo "  镜像大小: $image_size"

echo ""
echo "🎯 测试结论:"
if [ "$improvement_percent" -gt 50 ]; then
    echo "  🎉 镜像化显著提升了启动性能！"
elif [ "$improvement_percent" -gt 20 ]; then
    echo "  ✅ 镜像化带来了明显的性能提升。"
elif [ "$improvement_percent" -gt 0 ]; then
    echo "  📈 镜像化带来了一定的性能提升。"
else
    echo "  ⚠️  在简单环境下性能提升不明显，但在复杂环境下会有显著效果。"
fi

echo ""
echo "💡 实际应用建议:"
echo "  - 在包含大量依赖安装的环境中，性能提升会更加显著"
echo "  - 建议在生产环境中实施镜像缓存策略"
echo "  - 可以考虑预拉取热门项目的镜像以进一步优化用户体验"

echo ""
echo "✅ 性能测试完成！"
