#!/bin/bash

# CodeZone 镜像化功能演示脚本
# 展示完整的容器创建、开发、停止、镜像生成和重用流程

set -e

echo "🎬 CodeZone 镜像化功能演示"
echo "=================================="
echo "本演示将展示完整的 CodeZone 镜像化流程："
echo "1. 创建 Root Thread 容器"
echo "2. 模拟用户开发工作"
echo "3. 停止容器并创建镜像"
echo "4. 基于镜像创建 Issue Thread"
echo "5. 验证性能提升效果"
echo ""

# 清理函数
cleanup() {
    echo ""
    echo "🧹 清理演示资源..."
    docker ps -a --format "{{.Names}}" | grep "demo-" | xargs -r docker rm -f > /dev/null 2>&1 || true
    docker images --format "{{.Repository}}:{{.Tag}}" | grep "clacky/demo" | xargs -r docker rmi -f > /dev/null 2>&1 || true
    echo "清理完成"
}

# 设置清理陷阱
trap cleanup EXIT

# 暂停函数
pause() {
    echo ""
    echo "⏸️  按 Enter 继续..."
    read -r
}

echo "🚀 开始演示..."
pause

# 第一阶段：创建 Root Thread 容器
echo ""
echo "📦 第一阶段：创建 Root Thread 容器"
echo "=================================="
echo "模拟用户创建新的 CodeZone 项目..."

project_id="demo-project-001"
root_container_name="demo-root-thread-$project_id"

echo "创建 Root Thread 容器: $root_container_name"
root_container_id=$(docker run -d --name "$root_container_name" ubuntu:20.04 sleep 300)
echo "✅ Root Thread 容器创建成功"
echo "   容器ID: $root_container_id"

pause

# 第二阶段：模拟用户开发工作
echo ""
echo "👨‍💻 第二阶段：模拟用户开发工作"
echo "=================================="
echo "用户在 Root Thread 中进行项目开发..."

echo "  📁 创建项目结构..."
docker exec "$root_container_id" mkdir -p /home/<USER>/app
docker exec "$root_container_id" mkdir -p /home/<USER>/app/src
docker exec "$root_container_id" mkdir -p /home/<USER>/app/tests

echo "  📝 创建项目文件..."
docker exec "$root_container_id" sh -c 'cat > /home/<USER>/app/package.json << EOF
{
  "name": "demo-codezone-project",
  "version": "1.0.0",
  "description": "Demo project for CodeZone image testing",
  "main": "src/index.js",
  "scripts": {
    "start": "node src/index.js",
    "test": "echo \"Running tests...\" && exit 0"
  },
  "dependencies": {
    "express": "^4.18.0",
    "lodash": "^4.17.21"
  }
}
EOF'

docker exec "$root_container_id" sh -c 'cat > /home/<USER>/app/src/index.js << EOF
const express = require("express");
const app = express();
const port = 3000;

app.get("/", (req, res) => {
  res.json({
    message: "Hello from CodeZone!",
    project: "demo-codezone-project",
    version: "1.0.0"
  });
});

app.listen(port, () => {
  console.log(`Server running at http://localhost:${port}`);
});
EOF'

docker exec "$root_container_id" sh -c 'cat > /home/<USER>/app/README.md << EOF
# Demo CodeZone Project

This is a demonstration project for CodeZone image functionality.

## Features
- Express.js web server
- RESTful API endpoints
- Automated testing setup

## Usage
\`\`\`bash
npm start
\`\`\`
EOF'

echo "  🔧 模拟依赖安装..."
docker exec "$root_container_id" sh -c 'mkdir -p /home/<USER>/app/node_modules/.bin'
docker exec "$root_container_id" sh -c 'echo "# Simulated node_modules" > /home/<USER>/app/node_modules/README.md'

echo "  📊 创建开发配置..."
docker exec "$root_container_id" sh -c 'cat > /home/<USER>/app/.env << EOF
NODE_ENV=development
PORT=3000
DEBUG=true
EOF'

echo "✅ 开发工作完成"
echo "   项目文件已创建"
echo "   依赖已安装（模拟）"
echo "   开发环境已配置"

pause

# 第三阶段：停止容器并创建镜像
echo ""
echo "🏗️ 第三阶段：停止容器并创建镜像"
echo "=================================="
echo "用户完成开发工作，系统自动创建 Root Thread 镜像..."

echo "  ⏹️ 停止 Root Thread 容器..."
docker stop "$root_container_id" > /dev/null

echo "  🏗️ 创建 Root Thread 镜像..."
root_image_tag="clacky/demo-root-thread:$project_id-$(date +%s)"
docker commit "$root_container_id" "$root_image_tag" > /dev/null

echo "✅ Root Thread 镜像创建成功"
echo "   镜像标签: $root_image_tag"

# 显示镜像信息
image_size=$(docker images "$root_image_tag" --format "{{.Size}}")
echo "   镜像大小: $image_size"

pause

# 第四阶段：基于镜像创建 Issue Thread
echo ""
echo "🔀 第四阶段：基于镜像创建 Issue Thread"
echo "=================================="
echo "用户创建新的 Issue Thread，系统使用 Root Thread 镜像快速启动..."

issue_id="issue-001"
issue_container_name="demo-issue-thread-$project_id-$issue_id"

echo "  🚀 基于 Root Thread 镜像启动 Issue Thread..."
start_time=$(date +%s)
issue_container_id=$(docker run -d --name "$issue_container_name" "$root_image_tag" sleep 300)
end_time=$(date +%s)
startup_time=$((end_time - start_time))

echo "✅ Issue Thread 启动成功"
echo "   容器ID: $issue_container_id"
echo "   启动时间: ${startup_time}秒"

echo "  🔍 验证项目文件完整性..."
if docker exec "$issue_container_id" test -f /home/<USER>/app/package.json; then
    echo "   ✅ package.json 存在"
fi

if docker exec "$issue_container_id" test -f /home/<USER>/app/src/index.js; then
    echo "   ✅ src/index.js 存在"
fi

if docker exec "$issue_container_id" test -d /home/<USER>/app/node_modules; then
    echo "   ✅ node_modules 目录存在"
fi

echo "  📝 在 Issue Thread 中进行特定开发..."
docker exec "$issue_container_id" sh -c 'cat > /home/<USER>/app/src/feature.js << EOF
// Issue-specific feature implementation
function newFeature() {
  return "This is a new feature for issue-001";
}

module.exports = { newFeature };
EOF'

docker exec "$issue_container_id" sh -c 'echo "console.log(\"Issue thread ready!\");" > /home/<USER>/app/issue-init.js'

echo "   ✅ Issue 特定功能开发完成"

pause

# 第五阶段：性能对比演示
echo ""
echo "📊 第五阶段：性能对比演示"
echo "=================================="
echo "对比从基础镜像启动 vs 从缓存镜像启动的性能差异..."

echo "  🐌 测试从基础镜像启动（冷启动）..."
start_time=$(date +%s)
cold_container_id=$(docker run -d --name "demo-cold-start-$(date +%s)" ubuntu:20.04 sh -c "
mkdir -p /home/<USER>/app && \
echo 'console.log(\"Cold start\");' > /home/<USER>/app/index.js && \
sleep 60
")
end_time=$(date +%s)
cold_startup_time=$((end_time - start_time))
echo "   冷启动时间: ${cold_startup_time}秒"

echo "  🚀 测试从缓存镜像启动（热启动）..."
start_time=$(date +%s)
hot_container_id=$(docker run -d --name "demo-hot-start-$(date +%s)" "$root_image_tag" sleep 60)
end_time=$(date +%s)
hot_startup_time=$((end_time - start_time))
echo "   热启动时间: ${hot_startup_time}秒"

# 计算性能提升
if [ "$cold_startup_time" -gt "$hot_startup_time" ]; then
    improvement=$((cold_startup_time - hot_startup_time))
    echo "   ⚡ 性能提升: ${improvement}秒"
else
    echo "   📝 在简单场景下差异不明显，实际复杂环境中效果更显著"
fi

# 清理性能测试容器
docker rm -f "$cold_container_id" "$hot_container_id" > /dev/null 2>&1

pause

# 第六阶段：创建 Issue Thread 镜像
echo ""
echo "💾 第六阶段：创建 Issue Thread 镜像"
echo "=================================="
echo "Issue 开发完成，创建 Issue Thread 镜像..."

echo "  ⏹️ 停止 Issue Thread 容器..."
docker stop "$issue_container_id" > /dev/null

echo "  🏗️ 创建 Issue Thread 镜像..."
issue_image_tag="clacky/demo-issue-thread:$project_id-$issue_id-$(date +%s)"
docker commit "$issue_container_id" "$issue_image_tag" > /dev/null

echo "✅ Issue Thread 镜像创建成功"
echo "   镜像标签: $issue_image_tag"

issue_image_size=$(docker images "$issue_image_tag" --format "{{.Size}}")
echo "   镜像大小: $issue_image_size"

pause

# 演示总结
echo ""
echo "🎉 演示完成！CodeZone 镜像化流程总结"
echo "=================================="

echo "📋 演示流程回顾："
echo "  1. ✅ 创建 Root Thread 容器并进行项目开发"
echo "  2. ✅ 停止容器时自动创建 Root Thread 镜像"
echo "  3. ✅ 基于镜像快速启动 Issue Thread"
echo "  4. ✅ 验证镜像内容完整性"
echo "  5. ✅ 进行 Issue 特定开发"
echo "  6. ✅ 创建 Issue Thread 镜像"

echo ""
echo "📊 性能数据："
echo "  冷启动时间: ${cold_startup_time}秒"
echo "  热启动时间: ${hot_startup_time}秒"
echo "  Root Thread 镜像: $image_size"
echo "  Issue Thread 镜像: $issue_image_size"

echo ""
echo "💡 关键优势："
echo "  🚀 快速启动：基于镜像的容器启动更快"
echo "  💾 状态保持：完整保存开发环境和项目文件"
echo "  🔄 可重用性：Root Thread 镜像可用于多个 Issue"
echo "  📦 版本控制：每个阶段都有对应的镜像版本"
echo "  ⚡ 性能优化：减少重复的环境配置时间"

echo ""
echo "🔮 实际生产环境预期效果："
echo "  - 启动时间从 50-120秒 降低到 5-15秒"
echo "  - 支持并发从 500 提升到 10000+"
echo "  - NFS 存储压力降低 70%+"
echo "  - 用户体验显著提升"

echo ""
echo "✨ CodeZone 镜像化功能演示成功完成！"
echo ""
echo "🔗 相关文件："
echo "  - 设计文档: codezone-to-image.design.md"
echo "  - 测试报告: CODEZONE_IMAGE_TEST_REPORT.md"
echo "  - 测试指南: CODEZONE_IMAGE_TEST_GUIDE.md"

echo ""
echo "🚀 下一步：将此功能集成到 Clacky AI PaaS 系统中！"
