# CodeZone 镜像化功能测试指南

本指南详细说明如何测试 CodeZone 镜像化功能，包括容器创建、激活启动、停止创建镜像的完整流程。

## 🎯 测试目标

验证以下功能是否正常工作：
1. **容器创建和启动** - 创建新的 CodeZone 容器并成功启动
2. **容器停止和镜像生成** - 容器停止时自动创建镜像
3. **基于镜像的快速启动** - 使用已有镜像快速启动新容器
4. **智能调度** - 基于镜像缓存的亲和性调度
5. **性能优化** - 验证启动时间和缓存命中率

## 🛠️ 前置条件

### 1. 环境准备
```bash
# 确保开发环境正在运行
make dev-start

# 检查所有服务状态
docker ps
```

应该看到以下容器正在运行：
- `manager-app-dev` - 主应用
- `clacky-ai-paas-backend-mysql-1` - MySQL 数据库
- `clacky-ai-paas-backend-redis-1` - Redis 缓存
- `clacky-ai-paas-backend-rabbitmq-1` - RabbitMQ 消息队列

### 2. 验证服务健康状态
```bash
# 检查应用健康状态
curl -I http://localhost:8000/actuator/health

# 检查数据库连接
docker exec clacky-ai-paas-backend-mysql-1 mysql -u root -prd123456 -e "SELECT 1"

# 检查 Redis 连接
docker exec clacky-ai-paas-backend-redis-1 redis-cli ping
```

## 🧪 自动化测试

### 运行完整测试套件
```bash
# 给测试脚本执行权限
chmod +x test-codezone-image.sh

# 运行测试
./test-codezone-image.sh
```

### 单独运行测试
```bash
cd d42paas_manager

# 运行生命周期测试
./mvnw test -Dtest=CodeZoneImageLifecycleTest -Dspring.profiles.active=local

# 运行集成测试
./mvnw test -Dtest=CodeZoneImageIntegrationTest -Dspring.profiles.active=local
```

## 🔧 手动测试步骤

### 第一阶段：容器创建和启动测试

#### 1. 创建 Root Thread 容器
```bash
# 通过 API 创建容器
curl -X POST http://localhost:8000/api/containers \
  -H "Content-Type: application/json" \
  -d '{
    "projectId": 1001,
    "type": "ROOT_THREAD",
    "userId": "test-user-001",
    "environmentVersion": "latest"
  }'
```

#### 2. 验证容器状态
```bash
# 查看容器列表
curl http://localhost:8000/api/containers

# 检查 Docker 容器
docker ps | grep codezone
```

#### 3. 监控启动时间
```bash
# 查看应用日志
docker logs manager-app-dev --tail 50 -f
```

### 第二阶段：模拟用户开发工作

#### 1. 连接到容器
```bash
# 获取容器 ID
CONTAINER_ID=$(docker ps --format "table {{.ID}}\t{{.Names}}" | grep codezone | awk '{print $1}')

# 进入容器
docker exec -it $CONTAINER_ID /bin/bash
```

#### 2. 模拟开发活动
```bash
# 在容器内执行
mkdir -p /home/<USER>/app
cd /home/<USER>/app

# 创建项目文件
echo 'console.log("Hello CodeZone!");' > index.js
echo '{"name": "test-app", "version": "1.0.0"}' > package.json

# 安装依赖（模拟）
touch node_modules/.keep

# 创建一些开发文件
mkdir src
echo 'export default function hello() { return "Hello!"; }' > src/utils.js

# 退出容器
exit
```

### 第三阶段：容器停止和镜像生成

#### 1. 停止容器
```bash
# 通过 API 停止容器
curl -X POST http://localhost:8000/api/containers/{containerId}/stop

# 或直接停止 Docker 容器
docker stop $CONTAINER_ID
```

#### 2. 验证镜像自动创建
```bash
# 查看应用日志，寻找镜像创建日志
docker logs manager-app-dev | grep -i "image.*created\|commit\|push"

# 检查本地镜像
docker images | grep clacky
```

#### 3. 验证镜像推送（如果配置了镜像仓库）
```bash
# 查看推送日志
docker logs manager-app-dev | grep -i "push.*complete\|registry"
```

### 第四阶段：基于镜像的快速启动

#### 1. 创建 Issue Thread 容器
```bash
# 创建基于 Root Thread 的 Issue Thread
curl -X POST http://localhost:8000/api/containers \
  -H "Content-Type: application/json" \
  -d '{
    "projectId": 1001,
    "issueId": 2001,
    "type": "ISSUE_THREAD",
    "userId": "test-user-001",
    "parentImageTag": "clacky/root-thread:1001-{timestamp}"
  }'
```

#### 2. 测量启动时间
```bash
# 记录开始时间
START_TIME=$(date +%s%3N)

# 等待容器启动完成
while ! curl -s http://localhost:8000/api/containers/{containerId}/status | grep -q "RUNNING"; do
  sleep 1
done

# 计算启动时间
END_TIME=$(date +%s%3N)
STARTUP_TIME=$((END_TIME - START_TIME))
echo "容器启动时间: ${STARTUP_TIME}ms"
```

### 第五阶段：智能调度验证

#### 1. 创建多个容器测试调度
```bash
# 创建多个相同项目的容器
for i in {1..3}; do
  curl -X POST http://localhost:8000/api/containers \
    -H "Content-Type: application/json" \
    -d "{
      \"projectId\": 1001,
      \"issueId\": $((2000 + i)),
      \"type\": \"ISSUE_THREAD\",
      \"userId\": \"test-user-00$i\"
    }"
  sleep 2
done
```

#### 2. 验证调度决策
```bash
# 查看调度日志
docker logs manager-app-dev | grep -i "scheduler\|affinity\|cache.*hit"

# 检查容器分布
docker ps --format "table {{.Names}}\t{{.Status}}" | grep codezone
```

## 📊 性能指标验证

### 1. 启动时间对比
```bash
# 基础镜像启动时间（首次）
echo "测试基础镜像启动时间..."

# 缓存镜像启动时间（后续）
echo "测试缓存镜像启动时间..."
```

### 2. 缓存命中率
```bash
# 查看缓存统计
curl http://localhost:8000/api/metrics/image-cache

# 查看调度统计
curl http://localhost:8000/api/metrics/scheduler
```

### 3. 存储使用情况
```bash
# 查看镜像存储使用
docker system df

# 查看具体镜像大小
docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}" | grep clacky
```

## 🔍 故障排查

### 常见问题和解决方案

#### 1. 容器启动失败
```bash
# 检查 Docker 服务状态
systemctl status docker

# 查看详细错误日志
docker logs manager-app-dev | grep -i error

# 检查资源使用情况
docker stats
```

#### 2. 镜像创建失败
```bash
# 检查磁盘空间
df -h

# 查看 Docker 存储使用
docker system df

# 清理无用镜像
docker system prune -f
```

#### 3. 调度问题
```bash
# 检查 Docker Server 状态
curl http://localhost:8000/api/docker-servers

# 查看调度器日志
docker logs manager-app-dev | grep -i "scheduler\|select"
```

## 📈 预期结果

### 成功指标
- ✅ 容器创建成功率 > 95%
- ✅ 首次启动时间 < 60秒
- ✅ 缓存命中启动时间 < 20秒
- ✅ 镜像创建成功率 > 90%
- ✅ 调度决策时间 < 5秒

### 性能改进
- 🚀 启动时间减少 50%+
- 🎯 缓存命中率 > 70%
- 💾 存储效率提升 30%+

## 🧹 清理测试环境

### 清理测试容器和镜像
```bash
# 停止所有测试容器
docker ps | grep test-codezone | awk '{print $1}' | xargs -r docker stop

# 删除测试容器
docker ps -a | grep test-codezone | awk '{print $1}' | xargs -r docker rm

# 删除测试镜像
docker images | grep clacky/test | awk '{print $3}' | xargs -r docker rmi

# 清理系统
docker system prune -f
```

### 重置测试数据
```bash
# 清理数据库测试数据
docker exec clacky-ai-paas-backend-mysql-1 mysql -u root -prd123456 -e "
DELETE FROM docker_container WHERE user_id LIKE 'test-user-%';
DELETE FROM thread_image WHERE user_id LIKE 'test-user-%';
"
```

## 📝 测试报告

测试完成后，请记录以下信息：

1. **测试环境信息**
   - 操作系统版本
   - Docker 版本
   - 可用内存和磁盘空间

2. **性能数据**
   - 平均启动时间
   - 缓存命中率
   - 镜像大小统计

3. **问题和改进建议**
   - 遇到的问题
   - 性能瓶颈
   - 优化建议

---

**注意**: 这个测试指南基于设计文档中的功能规划。实际测试时，请根据当前实现的功能进行调整。
